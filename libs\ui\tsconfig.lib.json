{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "types": ["node"], "rootDir": "src", "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo"}, "exclude": ["out-tsc", "dist", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/test-setup.ts", "jest.config.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"]}