(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},993:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,385,23)),Promise.resolve().then(r.t.bind(r,3737,23)),Promise.resolve().then(r.t.bind(r,6081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,5856,23)),Promise.resolve().then(r.t.bind(r,5492,23)),Promise.resolve().then(r.t.bind(r,9082,23)),Promise.resolve().then(r.t.bind(r,5812,23))},2618:(e,t,r)=>{Promise.resolve().then(r.bind(r,5277))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3116:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>m,tree:()=>a});var n=r(4332),o=r(8819),s=r(7851),i=r.n(s),d=r(7540),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(t,l);let a={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,9033,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4356)),"C:\\dev\\dohoder\\apps\\dohoder-admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,2341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=[],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:a}})},3190:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4351:(e,t,r)=>{"use strict";r.d(t,{StyledComponentsRegistry:()=>n});let n=(0,r(3952).registerClientReference)(function(){throw Error("Attempted to call StyledComponentsRegistry() from the server but StyledComponentsRegistry is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\dev\\dohoder\\apps\\dohoder-admin\\app\\registry.tsx","StyledComponentsRegistry")},4356:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>s});var n=r(8828);r(3190);var o=r(4351);let s={title:"Welcome to demo2",description:"Generated by create-nx-workspace"};function i({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{children:(0,n.jsx)(o.StyledComponentsRegistry,{children:e})})})}},4466:(e,t,r)=>{Promise.resolve().then(r.bind(r,4351))},5222:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9355,23)),Promise.resolve().then(r.t.bind(r,4439,23)),Promise.resolve().then(r.t.bind(r,7851,23)),Promise.resolve().then(r.t.bind(r,4730,23)),Promise.resolve().then(r.t.bind(r,9774,23)),Promise.resolve().then(r.t.bind(r,3170,23)),Promise.resolve().then(r.t.bind(r,968,23)),Promise.resolve().then(r.t.bind(r,8298,23))},5277:(e,t,r)=>{"use strict";r.d(t,{StyledComponentsRegistry:()=>d});var n=r(3486),o=r(159),s=r(2984),i=r(5278);function d({children:e}){let[t]=(0,o.useState)(()=>new i.E);return(0,s.useServerInsertedHTML)(()=>{let e=t.getStyleElement();return t.instance.clearTag(),(0,n.jsx)(n.Fragment,{children:e})}),(0,n.jsx)(i.ID,{sheet:t.instance,children:e})}},7910:e=>{"use strict";e.exports=require("stream")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[50,465],()=>r(3116));module.exports=n})();