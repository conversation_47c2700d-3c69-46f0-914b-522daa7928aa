[{"name": "generate-buildid", "duration": 228, "timestamp": 1463742319838, "id": 4, "parentId": 1, "tags": {}, "startTime": 1759002764826, "traceId": "36d712a0d5cd0479"}, {"name": "load-custom-routes", "duration": 363, "timestamp": 1463742320188, "id": 5, "parentId": 1, "tags": {}, "startTime": 1759002764826, "traceId": "36d712a0d5cd0479"}, {"name": "create-dist-dir", "duration": 3377, "timestamp": 1463742578956, "id": 6, "parentId": 1, "tags": {}, "startTime": 1759002765085, "traceId": "36d712a0d5cd0479"}, {"name": "create-pages-mapping", "duration": 402, "timestamp": 1463742602176, "id": 7, "parentId": 1, "tags": {}, "startTime": 1759002765108, "traceId": "36d712a0d5cd0479"}, {"name": "collect-app-paths", "duration": 2407, "timestamp": 1463742602658, "id": 8, "parentId": 1, "tags": {}, "startTime": 1759002765108, "traceId": "36d712a0d5cd0479"}, {"name": "create-app-mapping", "duration": 759, "timestamp": 1463742605101, "id": 9, "parentId": 1, "tags": {}, "startTime": 1759002765111, "traceId": "36d712a0d5cd0479"}, {"name": "public-dir-conflict-check", "duration": 744, "timestamp": 1463742606289, "id": 10, "parentId": 1, "tags": {}, "startTime": 1759002765112, "traceId": "36d712a0d5cd0479"}, {"name": "generate-routes-manifest", "duration": 1948, "timestamp": 1463742607309, "id": 11, "parentId": 1, "tags": {}, "startTime": 1759002765113, "traceId": "36d712a0d5cd0479"}, {"name": "create-entrypoints", "duration": 35890, "timestamp": 1463742624690, "id": 14, "parentId": 1, "tags": {}, "startTime": 1759002765130, "traceId": "36d712a0d5cd0479"}, {"name": "generate-webpack-config", "duration": 227005, "timestamp": 1463742660697, "id": 15, "parentId": 13, "tags": {}, "startTime": 1759002765166, "traceId": "36d712a0d5cd0479"}, {"name": "next-trace-entrypoint-plugin", "duration": 2405, "timestamp": 1463743030164, "id": 17, "parentId": 16, "tags": {}, "startTime": 1759002765536, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 186041, "timestamp": 1463743039198, "id": 22, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1759002765545, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 225726, "timestamp": 1463743039159, "id": 20, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1759002765545, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 234076, "timestamp": 1463743039212, "id": 23, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1759002765545, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 276129, "timestamp": 1463743039185, "id": 21, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fapi%2Fhello%2Froute&name=app%2Fapi%2Fhello%2Froute&pagePath=private-next-app-dir%2Fapi%2Fhello%2Froute.ts&appDir=C%3A%5Cdev%5Cdohoder%5Capps%5Cdohoder-admin%5Capp&appPaths=%2Fapi%2Fhello%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1759002765545, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 278971, "timestamp": 1463743038700, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5Cdev%5Cdohoder%5Capps%5Cdohoder-admin%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1759002765544, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 278498, "timestamp": 1463743039224, "id": 24, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cdev%5Cdohoder%5Capps%5Cdohoder-admin%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1759002765545, "traceId": "36d712a0d5cd0479"}, {"name": "make", "duration": 355826, "timestamp": 1463743038328, "id": 18, "parentId": 16, "tags": {}, "startTime": 1759002765544, "traceId": "36d712a0d5cd0479"}, {"name": "get-entries", "duration": 1504, "timestamp": 1463743395650, "id": 40, "parentId": 39, "tags": {}, "startTime": 1759002765901, "traceId": "36d712a0d5cd0479"}, {"name": "node-file-trace-plugin", "duration": 69964, "timestamp": 1463743400522, "id": 41, "parentId": 39, "tags": {"traceEntryCount": "8"}, "startTime": 1759002765906, "traceId": "36d712a0d5cd0479"}, {"name": "collect-traced-files", "duration": 479, "timestamp": 1463743470501, "id": 42, "parentId": 39, "tags": {}, "startTime": 1759002765976, "traceId": "36d712a0d5cd0479"}, {"name": "finish-modules", "duration": 75554, "timestamp": 1463743395436, "id": 39, "parentId": 17, "tags": {}, "startTime": 1759002765901, "traceId": "36d712a0d5cd0479"}, {"name": "chunk-graph", "duration": 8633, "timestamp": 1463743493285, "id": 44, "parentId": 43, "tags": {}, "startTime": 1759002765999, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-modules", "duration": 30, "timestamp": 1463743502079, "id": 46, "parentId": 43, "tags": {}, "startTime": 1759002766008, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-chunks", "duration": 8765, "timestamp": 1463743502198, "id": 47, "parentId": 43, "tags": {}, "startTime": 1759002766008, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-tree", "duration": 158, "timestamp": 1463743511060, "id": 48, "parentId": 43, "tags": {}, "startTime": 1759002766017, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-chunk-modules", "duration": 5809, "timestamp": 1463743511310, "id": 49, "parentId": 43, "tags": {}, "startTime": 1759002766017, "traceId": "36d712a0d5cd0479"}, {"name": "optimize", "duration": 15210, "timestamp": 1463743502009, "id": 45, "parentId": 43, "tags": {}, "startTime": 1759002766008, "traceId": "36d712a0d5cd0479"}, {"name": "module-hash", "duration": 12335, "timestamp": 1463743530448, "id": 50, "parentId": 43, "tags": {}, "startTime": 1759002766036, "traceId": "36d712a0d5cd0479"}, {"name": "code-generation", "duration": 3004, "timestamp": 1463743542880, "id": 51, "parentId": 43, "tags": {}, "startTime": 1759002766049, "traceId": "36d712a0d5cd0479"}, {"name": "hash", "duration": 8937, "timestamp": 1463743550337, "id": 52, "parentId": 43, "tags": {}, "startTime": 1759002766056, "traceId": "36d712a0d5cd0479"}, {"name": "code-generation-jobs", "duration": 288, "timestamp": 1463743559271, "id": 53, "parentId": 43, "tags": {}, "startTime": 1759002766065, "traceId": "36d712a0d5cd0479"}, {"name": "module-assets", "duration": 211, "timestamp": 1463743559515, "id": 54, "parentId": 43, "tags": {}, "startTime": 1759002766065, "traceId": "36d712a0d5cd0479"}, {"name": "create-chunk-assets", "duration": 1755, "timestamp": 1463743559738, "id": 55, "parentId": 43, "tags": {}, "startTime": 1759002766066, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 503, "timestamp": 1463743573481, "id": 57, "parentId": 56, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1759002766079, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 262, "timestamp": 1463743573734, "id": 58, "parentId": 56, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 246, "timestamp": 1463743573753, "id": 59, "parentId": 56, "tags": {"name": "../app/api/hello/route.js", "cache": "HIT"}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 237, "timestamp": 1463743573764, "id": 60, "parentId": 56, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 229, "timestamp": 1463743573773, "id": 61, "parentId": 56, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 200, "timestamp": 1463743573805, "id": 62, "parentId": 56, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 192, "timestamp": 1463743573814, "id": 63, "parentId": 56, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 174, "timestamp": 1463743573834, "id": 64, "parentId": 56, "tags": {"name": "50.js", "cache": "HIT"}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 166, "timestamp": 1463743573844, "id": 65, "parentId": 56, "tags": {"name": "465.js", "cache": "HIT"}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 86, "timestamp": 1463743573925, "id": 66, "parentId": 56, "tags": {"name": "496.js", "cache": "HIT"}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "minify-webpack-plugin-optimize", "duration": 6157, "timestamp": 1463743567863, "id": 56, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1759002766074, "traceId": "36d712a0d5cd0479"}, {"name": "css-minimizer-plugin", "duration": 246, "timestamp": 1463743574179, "id": 67, "parentId": 16, "tags": {}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "create-trace-assets", "duration": 1938, "timestamp": 1463743574686, "id": 68, "parentId": 17, "tags": {}, "startTime": 1759002766080, "traceId": "36d712a0d5cd0479"}, {"name": "create-trace-assets", "duration": 803, "timestamp": 1463743576885, "id": 69, "parentId": 17, "tags": {}, "startTime": 1759002766083, "traceId": "36d712a0d5cd0479"}, {"name": "seal", "duration": 102292, "timestamp": 1463743482690, "id": 43, "parentId": 16, "tags": {}, "startTime": 1759002765988, "traceId": "36d712a0d5cd0479"}, {"name": "webpack-compilation", "duration": 561836, "timestamp": 1463743028177, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1759002765534, "traceId": "36d712a0d5cd0479"}, {"name": "emit", "duration": 21652, "timestamp": 1463743590817, "id": 70, "parentId": 13, "tags": {}, "startTime": 1759002766097, "traceId": "36d712a0d5cd0479"}, {"name": "webpack-close", "duration": 1093, "timestamp": 1463743614468, "id": 71, "parentId": 13, "tags": {"name": "server"}, "startTime": 1759002766120, "traceId": "36d712a0d5cd0479"}, {"name": "webpack-generate-error-stats", "duration": 3360, "timestamp": 1463743615633, "id": 72, "parentId": 71, "tags": {}, "startTime": 1759002766121, "traceId": "36d712a0d5cd0479"}, {"name": "make", "duration": 257, "timestamp": 1463743630121, "id": 74, "parentId": 73, "tags": {}, "startTime": 1759002766136, "traceId": "36d712a0d5cd0479"}, {"name": "chunk-graph", "duration": 41, "timestamp": 1463743631249, "id": 76, "parentId": 75, "tags": {}, "startTime": 1759002766137, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-modules", "duration": 10, "timestamp": 1463743631352, "id": 78, "parentId": 75, "tags": {}, "startTime": 1759002766137, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-chunks", "duration": 88, "timestamp": 1463743631431, "id": 79, "parentId": 75, "tags": {}, "startTime": 1759002766137, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-tree", "duration": 28, "timestamp": 1463743631576, "id": 80, "parentId": 75, "tags": {}, "startTime": 1759002766137, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-chunk-modules", "duration": 100, "timestamp": 1463743631687, "id": 81, "parentId": 75, "tags": {}, "startTime": 1759002766137, "traceId": "36d712a0d5cd0479"}, {"name": "optimize", "duration": 525, "timestamp": 1463743631310, "id": 77, "parentId": 75, "tags": {}, "startTime": 1759002766137, "traceId": "36d712a0d5cd0479"}, {"name": "module-hash", "duration": 17, "timestamp": 1463743632069, "id": 82, "parentId": 75, "tags": {}, "startTime": 1759002766138, "traceId": "36d712a0d5cd0479"}, {"name": "code-generation", "duration": 13, "timestamp": 1463743632098, "id": 83, "parentId": 75, "tags": {}, "startTime": 1759002766138, "traceId": "36d712a0d5cd0479"}, {"name": "hash", "duration": 91, "timestamp": 1463743632162, "id": 84, "parentId": 75, "tags": {}, "startTime": 1759002766138, "traceId": "36d712a0d5cd0479"}, {"name": "code-generation-jobs", "duration": 62, "timestamp": 1463743632253, "id": 85, "parentId": 75, "tags": {}, "startTime": 1759002766138, "traceId": "36d712a0d5cd0479"}, {"name": "module-assets", "duration": 30, "timestamp": 1463743632301, "id": 86, "parentId": 75, "tags": {}, "startTime": 1759002766138, "traceId": "36d712a0d5cd0479"}, {"name": "create-chunk-assets", "duration": 25, "timestamp": 1463743632339, "id": 87, "parentId": 75, "tags": {}, "startTime": 1759002766138, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 67, "timestamp": 1463743655737, "id": 89, "parentId": 88, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1759002766162, "traceId": "36d712a0d5cd0479"}, {"name": "minify-webpack-plugin-optimize", "duration": 11371, "timestamp": 1463743644445, "id": 88, "parentId": 73, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1759002766150, "traceId": "36d712a0d5cd0479"}, {"name": "css-minimizer-plugin", "duration": 7, "timestamp": 1463743655917, "id": 90, "parentId": 73, "tags": {}, "startTime": 1759002766162, "traceId": "36d712a0d5cd0479"}, {"name": "seal", "duration": 27435, "timestamp": 1463743631059, "id": 75, "parentId": 73, "tags": {}, "startTime": 1759002766137, "traceId": "36d712a0d5cd0479"}, {"name": "webpack-compilation", "duration": 30457, "timestamp": 1463743628191, "id": 73, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1759002766134, "traceId": "36d712a0d5cd0479"}, {"name": "emit", "duration": 2399, "timestamp": 1463743658734, "id": 91, "parentId": 13, "tags": {}, "startTime": 1759002766165, "traceId": "36d712a0d5cd0479"}, {"name": "webpack-close", "duration": 236, "timestamp": 1463743661721, "id": 92, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1759002766168, "traceId": "36d712a0d5cd0479"}, {"name": "webpack-generate-error-stats", "duration": 732, "timestamp": 1463743661973, "id": 93, "parentId": 92, "tags": {}, "startTime": 1759002766168, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 95753, "timestamp": 1463743676966, "id": 104, "parentId": 95, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1759002766183, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 180415, "timestamp": 1463743676925, "id": 99, "parentId": 95, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1759002766183, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 219809, "timestamp": 1463743676945, "id": 101, "parentId": 95, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1759002766183, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 219845, "timestamp": 1463743676938, "id": 100, "parentId": 95, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1759002766183, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 219831, "timestamp": 1463743676957, "id": 102, "parentId": 95, "tags": {"request": "C:\\dev\\dohoder\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1759002766183, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 224566, "timestamp": 1463743676878, "id": 98, "parentId": 95, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1759002766183, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 225729, "timestamp": 1463743676707, "id": 97, "parentId": 95, "tags": {"request": "./../../node_modules/next/dist/client/app-next.js"}, "startTime": 1759002766183, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 226183, "timestamp": 1463743676605, "id": 96, "parentId": 95, "tags": {"request": "./../../node_modules/next/dist/client/next.js"}, "startTime": 1759002766182, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 225883, "timestamp": 1463743676962, "id": 103, "parentId": 95, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Capps%5C%5Cdohoder-admin%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Capps%5C%5Cdohoder-admin%5C%5Capp%5C%5Cregistry.tsx%22%2C%22ids%22%3A%5B%22StyledComponentsRegistry%22%5D%7D&server=false!"}, "startTime": 1759002766183, "traceId": "36d712a0d5cd0479"}, {"name": "add-entry", "duration": 225887, "timestamp": 1463743676976, "id": 105, "parentId": 95, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cdohoder%5C%5Capps%5C%5Cdohoder-admin%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1759002766183, "traceId": "36d712a0d5cd0479"}, {"name": "make", "duration": 226708, "timestamp": 1463743676233, "id": 95, "parentId": 94, "tags": {}, "startTime": 1759002766182, "traceId": "36d712a0d5cd0479"}, {"name": "chunk-graph", "duration": 7191, "timestamp": 1463743915077, "id": 107, "parentId": 106, "tags": {}, "startTime": 1759002766421, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-modules", "duration": 9, "timestamp": 1463743922337, "id": 109, "parentId": 106, "tags": {}, "startTime": 1759002766428, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-chunks", "duration": 3870, "timestamp": 1463743924188, "id": 111, "parentId": 106, "tags": {}, "startTime": 1759002766430, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-tree", "duration": 9, "timestamp": 1463743928104, "id": 112, "parentId": 106, "tags": {}, "startTime": 1759002766434, "traceId": "36d712a0d5cd0479"}, {"name": "optimize-chunk-modules", "duration": 1556, "timestamp": 1463743928135, "id": 113, "parentId": 106, "tags": {}, "startTime": 1759002766434, "traceId": "36d712a0d5cd0479"}, {"name": "optimize", "duration": 7407, "timestamp": 1463743922314, "id": 108, "parentId": 106, "tags": {}, "startTime": 1759002766428, "traceId": "36d712a0d5cd0479"}, {"name": "module-hash", "duration": 9034, "timestamp": 1463743940530, "id": 114, "parentId": 106, "tags": {}, "startTime": 1759002766446, "traceId": "36d712a0d5cd0479"}, {"name": "code-generation", "duration": 3062, "timestamp": 1463743949609, "id": 115, "parentId": 106, "tags": {}, "startTime": 1759002766455, "traceId": "36d712a0d5cd0479"}, {"name": "hash", "duration": 6652, "timestamp": 1463743956039, "id": 116, "parentId": 106, "tags": {}, "startTime": 1759002766462, "traceId": "36d712a0d5cd0479"}, {"name": "code-generation-jobs", "duration": 167, "timestamp": 1463743962690, "id": 117, "parentId": 106, "tags": {}, "startTime": 1759002766468, "traceId": "36d712a0d5cd0479"}, {"name": "module-assets", "duration": 167, "timestamp": 1463743962833, "id": 118, "parentId": 106, "tags": {}, "startTime": 1759002766469, "traceId": "36d712a0d5cd0479"}, {"name": "create-chunk-assets", "duration": 1953, "timestamp": 1463743963008, "id": 119, "parentId": 106, "tags": {}, "startTime": 1759002766469, "traceId": "36d712a0d5cd0479"}, {"name": "NextJsBuildManifest-generateClientManifest", "duration": 1355, "timestamp": 1463743966933, "id": 121, "parentId": 94, "tags": {}, "startTime": 1759002766473, "traceId": "36d712a0d5cd0479"}, {"name": "NextJsBuildManifest-createassets", "duration": 2022, "timestamp": 1463743966285, "id": 120, "parentId": 94, "tags": {}, "startTime": 1759002766472, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 1161, "timestamp": 1463743970534, "id": 123, "parentId": 122, "tags": {"name": "static/chunks/main-f46e5fe0170ad19c.js", "cache": "HIT"}, "startTime": 1759002766476, "traceId": "36d712a0d5cd0479"}, {"name": "minify-js", "duration": 1128, "timestamp": 1463743970578, "id": 124, "parentId": 122, "tags": {"name": "static/chunks/main-app-54126e135df4e7ac.js", "cache": "HIT"}, "startTime": 1759002766476, "traceId": "36d712a0d5cd0479"}]