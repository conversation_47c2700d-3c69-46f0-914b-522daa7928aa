{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": ["{workspaceRoot}/bitbucket-pipelines.yml"]}, "nxCloudId": "68d80e36b3db98757902414c", "plugins": [{"plugin": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"plugin": "@nx/expo/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "prebuildTargetName": "prebuild", "serveTargetName": "serve", "installTargetName": "install", "exportTargetName": "export", "submitTargetName": "submit", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "devTargetName": "dev", "serveStaticTargetName": "serve-static", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/react-native/plugin", "options": {"startTargetName": "start", "upgradeTargetName": "update", "bundleTargetName": "bundle", "podInstallTargetName": "pod-install", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildIosTargetName": "build-ios", "buildAndroidTargetName": "build-android", "syncDepsTargetName": "sync-deps"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}}], "defaultBase": "origin/main", "generators": {"@nx/next": {"application": {"style": "styled-components", "linter": "eslint"}}}, "targetDefaults": {"@nx/esbuild:esbuild": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/js:swc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "test": {"dependsOn": ["^build"]}}}