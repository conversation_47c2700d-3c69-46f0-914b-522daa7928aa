(()=>{var e={};e.id=166,e.ids=[166],e.modules={408:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1193:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>c,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var o={};t.r(o),t.d(o,{GET:()=>i});var s=t(8106),n=t(8819),a=t(2050);async function i(e){return new Response("Hello, from API!")}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/hello/route",pathname:"/api/hello",filename:"route",bundlePath:"app/api/hello/route"},resolvedPagePath:"C:\\dev\\dohoder\\apps\\dohoder-admin\\app\\api\\hello\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:l}=p;function c(){return(0,a.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7032:()=>{},8106:(e,r,t)=>{"use strict";e.exports=t(4870)},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[50],()=>t(1193));module.exports=o})();