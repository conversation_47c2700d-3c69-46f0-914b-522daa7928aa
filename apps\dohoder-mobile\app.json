{"expo": {"name": "DohoderMobile", "slug": "@dohoder/dohoder-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "@dohoder/dohoder-mobile", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]]}}