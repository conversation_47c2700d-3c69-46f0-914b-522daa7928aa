{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug @dohoder/calendar-sync-api with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "@dohoder/calendar-sync-api"], "env": {"NODE_OPTIONS": "--inspect=9229"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/calendar-sync-api/dist/**/*.(m|c|)js", "!**/node_modules/**"]}]}