{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./out-tsc/jest", "jsx": "react-jsx", "types": ["jest", "node"], "module": "esnext", "moduleResolution": "bundler"}, "files": ["src/test-setup.ts"], "include": ["jest.config.ts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/**/*.d.ts"], "references": [{"path": "./tsconfig.lib.json"}]}