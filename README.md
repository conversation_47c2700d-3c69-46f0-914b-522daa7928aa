# DOHODER Monorepo

## 🚀 Prehľad

Tento repozitár je monolitické úložisko spravované pomocou **NX**, ktor<PERSON> združuje všetky aplikácie a zdieľané knižnice pre projekt **DOHODER**.

Projekt je zameraný na mobilné riešenie **Mobile-First** s Next.js administráciou a Node.js API pre kritické úlohy. Ako dátov<PERSON> vrstva slúži **Supabase**.

---

## ⚙️ Rýchla Inicializácia (Setup)

### 1. Inštalácia Závislostí

Spustite inštaláciu všetkých závislostí:

```bash
npm install
```

### 2. Konfigurácia Prostredia

Vytvorte súbor .env v koreňovom adresári a doplňte URL a kľúč pre Supabase:

#### .env (Vytvorte v koreni projektu)

```bash
SUPABASE_URL="https://[VASE_SUPABASE_ID].supabase.co"
SUPABASE_ANON_KEY="[VASE_ANON_KEY]"
```

### 3. Spustenie Aplikácií

Na spustenie jednotlivých aplikácií použite skripty definované v package.json:

| Aplikácia          | Spúšťací Skript              | Účel                                                  |
| ------------------ | ---------------------------- | ----------------------------------------------------- |
| Mobil (Expo)       | `npm run start:app`          | Spustí Expo server pre mobilnú aplikáciu.             |
| Admin (Next.js)    | `npm run start:admin`        | Spustí Next.js server pre administráciu v Dev režime. |
| API (Node/Express) | `npm run start:calendar-api` | Spustí Node.js API pre synchronizáciu kalendára.      |

### 🏗️ Adresárová Štruktúra

Štruktúra je rozdelená na apps/ (spúšťateľné projekty) a libs/ (opakovateľne použiteľný kód).

```
/DOHODER (Koreňový Priečinok)
├── apps/
│   ├── calendar-sync-api/      # Node.js Express Server (Synchronizácia)
│   ├── dohoder-admin/          # Next.js App Router (Administrácia)
│   └── dohoder-mobile/         # Expo / React Native (Hlavná Aplikácia)
│
├── libs/
│   ├── data-access/            # Dátová vrstva (Supabase klient, React Query hooky, API volania)
│   │   └── src/supabase/       # Miesto pre Supabase konfiguráciu a hooky
│   ├── domain/                 # Biznis logika a typy
│   │   └── src/auth/           # Zdieľané typy a modely pre autentifikáciu
│   ├── ui/                     # Dizajnový systém a zdieľané komponenty
│   │   └── src/design-system/  # Témy a nastavenia pre React Native Paper
│   └── util/                   # Zdieľané utility a pomocné funkcie (helpers)
│
├── nx.json                     # Centrálna konfigurácia NX
└── package.json                # Skripty a závislosti
```

### ✅ Stav a Testovanie

#### Testovacie Skripty (CLI)

Na spustenie testov pre konkrétnu knižnicu alebo aplikáciu použite:

```
# Príklad: Spustenie testov pre dátovú vrstvu (Vitest)
nx run @dohoder/data-access:test
```
