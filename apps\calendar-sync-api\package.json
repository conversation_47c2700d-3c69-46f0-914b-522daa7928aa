{"name": "@dohoder/calendar-sync-api", "version": "0.0.1", "private": true, "nx": {"targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "apps/calendar-sync-api/dist", "format": ["cjs"], "bundle": false, "main": "apps/calendar-sync-api/src/main.ts", "tsConfig": "apps/calendar-sync-api/tsconfig.app.json", "assets": ["apps/calendar-sync-api/src/assets"], "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "prune-lockfile": {"dependsOn": ["build"], "cache": true, "executor": "@nx/js:prune-lockfile", "outputs": ["{workspaceRoot}/apps/calendar-sync-api/dist/package.json", "{workspaceRoot}/apps/calendar-sync-api/dist/package-lock.json"], "options": {"buildTarget": "build"}}, "copy-workspace-modules": {"dependsOn": ["build"], "cache": true, "outputs": ["{workspaceRoot}/apps/calendar-sync-api/dist/workspace_modules"], "executor": "@nx/js:copy-workspace-modules", "options": {"buildTarget": "build"}}, "prune": {"dependsOn": ["prune-lockfile", "copy-workspace-modules"], "executor": "nx:noop"}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@dohoder/calendar-sync-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@dohoder/calendar-sync-api:build:development"}, "production": {"buildTarget": "@dohoder/calendar-sync-api:build:production"}}}}}, "dependencies": {"express": "^4.21.2"}}