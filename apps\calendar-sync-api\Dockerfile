# --- STAGE 1: Dependency Installation ---
# Použijeme najnovšiu LTS verziu Node (napr. 20)
FROM node:20-alpine AS deps

# Nastavíme pracovný adresár
WORKDIR /app

# Skopírujeme koreňové súbory package.json a package-lock.json (pre monorepo)
COPY package.json package-lock.json ./
RUN npm install --legacy-peer-deps

# --- STAGE 2: Build Application ---
FROM node:20-alpine AS builder

# Kopírujeme nainštalované závislosti z predchádzajúcej fázy
COPY --from=deps /app/node_modules ./node_modules
WORKDIR /app

# Skopírujeme celú monorepo štruktúru (je potrebná pre NX)
COPY . .

# Spustíme build aplikácie pomocou NX (generuje kód do dist/)
# Táto časť sa spustí pri BUILD fáze v Dockeri
RUN npx nx build calendar-sync-api

# --- STAGE 3: Production Image ---
FROM node:20-alpine AS production

# Nastavíme produkčné prostredie
ENV NODE_ENV production
WORKDIR /app

# Skopírujeme IBA vygenerovaný produkčný kód z dist/
# a node_modules, ktoré sú potrebné na spustenie
COPY --from=builder /app/apps/calendar-sync-api/dist/ ./
COPY --from=deps /app/node_modules ./node_modules

# Štandardný port pre Express.js
EXPOSE 3000

# Spustenie aplikácie
# Poznámka: Cesta k serveru musí byť presná podľa Vášho NX výstupu (index.js)
CMD [ "node", "main.js" ]